"""
配置模块

定义了智能体的配置类，包含各种模型设置和参数。
支持从环境变量和RunnableConfig中加载配置。
"""

import os
from pydantic import BaseModel, Field
from typing import Any, Optional

from langchain_core.runnables import RunnableConfig


class Configuration(BaseModel):
    """智能体配置类

    定义了智能体运行所需的所有配置参数，包括模型选择和研究参数。
    """

    query_generator_model: str = Field(
        default="gemini-2.0-flash",
        metadata={
            "description": "用于智能体查询生成的语言模型名称"
        },
    )

    reflection_model: str = Field(
        default="gemini-2.5-flash",
        metadata={
            "description": "用于智能体反思的语言模型名称"
        },
    )

    answer_model: str = Field(
        default="gemini-2.5-pro",
        metadata={
            "description": "用于智能体答案生成的语言模型名称"
        },
    )

    number_of_initial_queries: int = Field(
        default=3,
        metadata={"description": "要生成的初始搜索查询数量"},
    )

    max_research_loops: int = Field(
        default=2,
        metadata={"description": "要执行的最大研究循环次数"},
    )

    @classmethod
    def from_runnable_config(
        cls, config: Optional[RunnableConfig] = None
    ) -> "Configuration":
        """从RunnableConfig创建Configuration实例

        Args:
            config: 可选的RunnableConfig对象

        Returns:
            Configuration实例
        """
        # 从配置中提取可配置参数，如果没有则使用空字典
        configurable = (
            config["configurable"] if config and "configurable" in config else {}
        )

        # 从环境变量或配置中获取原始值
        raw_values: dict[str, Any] = {
            name: os.environ.get(name.upper(), configurable.get(name))
            for name in cls.model_fields.keys()
        }

        # 过滤掉None值
        values = {k: v for k, v in raw_values.items() if v is not None}

        return cls(**values)
