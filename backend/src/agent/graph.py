# 导入必要的库和模块
import os

# 导入自定义的工具和数据结构
from agent.tools_and_schemas import SearchQueryList, Reflection
# 导入环境变量加载器
from dotenv import load_dotenv
# 导入LangChain核心消息类型
from langchain_core.messages import AIMessage
# 导入LangGraph相关类型和图构建器
from langgraph.types import Send
from langgraph.graph import StateGraph
from langgraph.graph import START, END
from langchain_core.runnables import RunnableConfig
# 导入Google Generative AI客户端
from google.genai import Client

# 导入状态定义
from agent.state import (
    OverallState,
    QueryGenerationState,
    ReflectionState,
    WebSearchState,
)
# 导入配置类
from agent.configuration import Configuration
# 导入提示词模板
from agent.prompts import (
    get_current_date,
    query_writer_instructions,
    web_searcher_instructions,
    reflection_instructions,
    answer_instructions,
)
# 导入Google Generative AI聊天模型
from langchain_google_genai import ChatGoogleGenerativeAI
# 导入工具函数
from agent.utils import (
    get_citations,
    get_research_topic,
    insert_citation_markers,
    resolve_urls,
)

# 加载环境变量
load_dotenv()

# 检查Gemini API密钥是否设置
if os.getenv("GEMINI_API_KEY") is None:
    raise ValueError("GEMINI_API_KEY is not set")

# 创建Google Generative AI客户端，用于Google搜索API
genai_client = Client(api_key=os.getenv("GEMINI_API_KEY"))


# 图节点定义
def generate_query(state: OverallState, config: RunnableConfig) -> QueryGenerationState:
    """生成搜索查询的LangGraph节点

    基于用户问题使用Gemini 2.0 Flash生成优化的网络搜索查询。
    这是研究流程的第一步，将用户的问题转换为多个具体的搜索查询。

    Args:
        state: 当前图状态，包含用户的问题
        config: 可运行配置，包括LLM提供商设置

    Returns:
        包含状态更新的字典，其中search_query键包含生成的查询列表
    """
    # 从配置中获取可配置参数
    configurable = Configuration.from_runnable_config(config)

    # 检查是否有自定义的初始搜索查询数量，如果没有则使用配置中的默认值
    if state.get("initial_search_query_count") is None:
        state["initial_search_query_count"] = configurable.number_of_initial_queries

    # 初始化Gemini 2.0 Flash模型
    llm = ChatGoogleGenerativeAI(
        model=configurable.query_generator_model,  # 使用配置中指定的查询生成模型
        temperature=1.0,  # 设置较高的温度以增加查询的多样性
        max_retries=2,    # 最大重试次数
        api_key=os.getenv("GEMINI_API_KEY"),  # 从环境变量获取API密钥
    )
    # 配置结构化输出，确保返回SearchQueryList格式的数据
    structured_llm = llm.with_structured_output(SearchQueryList)

    # 格式化提示词
    current_date = get_current_date()  # 获取当前日期
    formatted_prompt = query_writer_instructions.format(
        current_date=current_date,
        research_topic=get_research_topic(state["messages"]),  # 从消息中提取研究主题
        number_queries=state["initial_search_query_count"],    # 要生成的查询数量
    )
    # 生成搜索查询
    result = structured_llm.invoke(formatted_prompt)
    return {"search_query": result.query}


def continue_to_web_research(state: QueryGenerationState):
    """将搜索查询发送到网络研究节点的LangGraph节点

    这个函数用于生成多个并行的网络研究节点，每个搜索查询对应一个节点。
    通过并行处理可以提高研究效率。
    """
    return [
        Send("web_research", {"search_query": search_query, "id": int(idx)})
        for idx, search_query in enumerate(state["search_query"])
    ]


def web_research(state: WebSearchState, config: RunnableConfig) -> OverallState:
    """执行网络研究的LangGraph节点

    使用原生Google搜索API工具结合Gemini 2.0 Flash执行网络搜索。
    这个节点负责实际的信息收集和处理。

    Args:
        state: 当前图状态，包含搜索查询和研究循环计数
        config: 可运行配置，包括搜索API设置

    Returns:
        包含状态更新的字典，包括sources_gathered、research_loop_count和web_research_results
    """
    # 配置参数
    configurable = Configuration.from_runnable_config(config)
    # 格式化搜索提示词
    formatted_prompt = web_searcher_instructions.format(
        current_date=get_current_date(),
        research_topic=state["search_query"],
    )

    # 使用Google GenAI客户端，因为LangChain客户端不返回grounding元数据
    response = genai_client.models.generate_content(
        model=configurable.query_generator_model,
        contents=formatted_prompt,
        config={
            "tools": [{"google_search": {}}],  # 启用Google搜索工具
            "temperature": 0,  # 设置温度为0以获得更确定性的结果
        },
    )
    # 将URL解析为短URL以节省token和时间
    resolved_urls = resolve_urls(
        response.candidates[0].grounding_metadata.grounding_chunks, state["id"]
    )
    # 获取引用并将其添加到生成的文本中
    citations = get_citations(response, resolved_urls)
    modified_text = insert_citation_markers(response.text, citations)
    # 从引用中提取所有来源信息
    sources_gathered = [item for citation in citations for item in citation["segments"]]

    return {
        "sources_gathered": sources_gathered,
        "search_query": [state["search_query"]],
        "web_research_result": [modified_text],
    }


def reflection(state: OverallState, config: RunnableConfig) -> ReflectionState:
    """识别知识缺口并生成后续查询的LangGraph节点

    分析当前摘要以识别需要进一步研究的领域，并生成潜在的后续查询。
    使用结构化输出以JSON格式提取后续查询。

    Args:
        state: 当前图状态，包含运行摘要和研究主题
        config: 可运行配置，包括LLM提供商设置

    Returns:
        包含状态更新的字典，包括search_query键，其中包含生成的后续查询
    """
    # 从配置中获取可配置参数
    configurable = Configuration.from_runnable_config(config)
    # 增加研究循环计数并获取推理模型
    state["research_loop_count"] = state.get("research_loop_count", 0) + 1
    reasoning_model = state.get("reasoning_model", configurable.reflection_model)

    # 格式化提示词
    current_date = get_current_date()
    formatted_prompt = reflection_instructions.format(
        current_date=current_date,
        research_topic=get_research_topic(state["messages"]),
        summaries="\n\n---\n\n".join(state["web_research_result"]),  # 将所有研究结果合并
    )
    # 初始化推理模型
    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=1.0,  # 设置较高温度以增加创造性思考
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    # 使用结构化输出获取反思结果
    result = llm.with_structured_output(Reflection).invoke(formatted_prompt)

    return {
        "is_sufficient": result.is_sufficient,           # 信息是否充足
        "knowledge_gap": result.knowledge_gap,           # 知识缺口描述
        "follow_up_queries": result.follow_up_queries,   # 后续查询列表
        "research_loop_count": state["research_loop_count"],  # 研究循环计数
        "number_of_ran_queries": len(state["search_query"]),  # 已运行的查询数量
    }


def evaluate_research(
    state: ReflectionState,
    config: RunnableConfig,
) -> OverallState:
    """评估研究进度并决定下一步的LangGraph路由函数

    通过决定是否继续收集信息或基于配置的最大研究循环数来完成摘要，
    从而控制研究循环。

    Args:
        state: 当前图状态，包含研究循环计数
        config: 可运行配置，包括max_research_loops设置

    Returns:
        字符串字面量，指示要访问的下一个节点（"web_research"或"finalize_answer"）
    """
    # 从配置中获取可配置参数
    configurable = Configuration.from_runnable_config(config)
    # 获取最大研究循环数，优先使用状态中的设置，否则使用配置中的默认值
    max_research_loops = (
        state.get("max_research_loops")
        if state.get("max_research_loops") is not None
        else configurable.max_research_loops
    )
    # 判断是否应该结束研究：信息充足或达到最大循环次数
    if state["is_sufficient"] or state["research_loop_count"] >= max_research_loops:
        return "finalize_answer"
    else:
        # 继续研究，为每个后续查询创建新的web_research任务
        return [
            Send(
                "web_research",
                {
                    "search_query": follow_up_query,
                    "id": state["number_of_ran_queries"] + int(idx),  # 为每个查询分配唯一ID
                },
            )
            for idx, follow_up_query in enumerate(state["follow_up_queries"])
        ]


def finalize_answer(state: OverallState, config: RunnableConfig):
    """完成研究摘要的LangGraph节点

    通过去重和格式化来源，然后将它们与运行摘要结合，
    创建一个结构良好的带有适当引用的研究报告来准备最终输出。

    Args:
        state: 当前图状态，包含运行摘要和收集的来源

    Returns:
        包含状态更新的字典，包括running_summary键，其中包含格式化的最终摘要和来源
    """
    # 从配置中获取可配置参数
    configurable = Configuration.from_runnable_config(config)
    # 获取推理模型，优先使用状态中的设置，否则使用配置中的答案模型
    reasoning_model = state.get("reasoning_model") or configurable.answer_model

    # 格式化提示词
    current_date = get_current_date()
    formatted_prompt = answer_instructions.format(
        current_date=current_date,
        research_topic=get_research_topic(state["messages"]),
        summaries="\n---\n\n".join(state["web_research_result"]),  # 合并所有研究结果
    )

    # 初始化推理模型，默认使用Gemini 2.5 Flash
    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0,  # 设置温度为0以获得更确定性的答案
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    result = llm.invoke(formatted_prompt)

    # 将短URL替换为原始URL，并将所有使用的URL添加到sources_gathered中
    unique_sources = []
    for source in state["sources_gathered"]:
        if source["short_url"] in result.content:
            # 在结果内容中替换短URL为完整URL
            result.content = result.content.replace(
                source["short_url"], source["value"]
            )
            unique_sources.append(source)

    return {
        "messages": [AIMessage(content=result.content)],  # 将结果包装为AI消息
        "sources_gathered": unique_sources,               # 返回实际使用的来源
    }


# 创建智能体图
builder = StateGraph(OverallState, config_schema=Configuration)

# 定义我们将在其间循环的节点
builder.add_node("generate_query", generate_query)      # 查询生成节点
builder.add_node("web_research", web_research)          # 网络研究节点
builder.add_node("reflection", reflection)              # 反思节点
builder.add_node("finalize_answer", finalize_answer)    # 最终答案节点

# 设置入口点为`generate_query`
# 这意味着这个节点是第一个被调用的
builder.add_edge(START, "generate_query")

# 添加条件边以在并行分支中继续搜索查询
builder.add_conditional_edges(
    "generate_query", continue_to_web_research, ["web_research"]
)

# 对网络研究进行反思
builder.add_edge("web_research", "reflection")

# 评估研究结果
builder.add_conditional_edges(
    "reflection", evaluate_research, ["web_research", "finalize_answer"]
)

# 完成答案
builder.add_edge("finalize_answer", END)

# 编译图并命名为"pro-search-agent"
graph = builder.compile(name="pro-search-agent")
