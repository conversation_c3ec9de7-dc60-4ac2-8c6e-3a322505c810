"""
状态定义模块

定义了LangGraph中使用的各种状态类型，用于在不同节点之间传递数据。
每个状态类都定义了特定阶段所需的数据结构。
"""

from __future__ import annotations

from dataclasses import dataclass, field
from typing import TypedDict

from langgraph.graph import add_messages
from typing_extensions import Annotated

import operator


class OverallState(TypedDict):
    """整体状态类

    定义了整个研究流程中需要维护的所有状态信息。
    使用Annotated类型来指定列表字段的合并策略。
    """
    messages: Annotated[list, add_messages]                    # 消息列表，使用add_messages合并策略
    search_query: Annotated[list, operator.add]               # 搜索查询列表，使用add操作合并
    web_research_result: Annotated[list, operator.add]        # 网络研究结果列表
    sources_gathered: Annotated[list, operator.add]           # 收集的来源列表
    initial_search_query_count: int                           # 初始搜索查询数量
    max_research_loops: int                                    # 最大研究循环次数
    research_loop_count: int                                   # 当前研究循环计数
    reasoning_model: str                                       # 推理模型名称


class ReflectionState(TypedDict):
    """反思状态类

    用于反思节点的状态，包含对当前研究结果的评估信息。
    """
    is_sufficient: bool                                        # 信息是否充足
    knowledge_gap: str                                         # 知识缺口描述
    follow_up_queries: Annotated[list, operator.add]          # 后续查询列表
    research_loop_count: int                                   # 研究循环计数
    number_of_ran_queries: int                                 # 已运行的查询数量


class Query(TypedDict):
    """查询类

    定义单个搜索查询的结构，包含查询内容和理由。
    """
    query: str                                                 # 查询字符串
    rationale: str                                             # 查询理由


class QueryGenerationState(TypedDict):
    """查询生成状态类

    用于查询生成节点的状态，包含生成的搜索查询列表。
    """
    search_query: list[Query]                                  # 查询对象列表


class WebSearchState(TypedDict):
    """网络搜索状态类

    用于网络搜索节点的状态，包含单个搜索查询和唯一标识符。
    """
    search_query: str                                          # 搜索查询字符串
    id: str                                                    # 唯一标识符


@dataclass(kw_only=True)
class SearchStateOutput:
    """搜索状态输出类

    定义搜索过程的最终输出格式。
    """
    running_summary: str = field(default=None)                # 最终报告
